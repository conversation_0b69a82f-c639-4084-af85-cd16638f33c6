'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAssessment } from '../../contexts/AssessmentContext';
import { assessmentTypes } from '../../data/assessmentQuestions';
import { validateAnswers } from '../../utils/assessment-calculations';
import { submitAssessment } from '../../services/assessment-api';

interface AssessmentHeaderProps {
  currentQuestion?: number;
  totalQuestions?: number;
  assessmentName?: string;
  phase?: string;
}

export default function AssessmentHeader({
  currentQuestion = 1,
  totalQuestions = 44,
  assessmentName = "Big Five Personality",
  phase = "Phase 1"
}: AssessmentHeaderProps) {
  const router = useRouter();
  const { debugFillAllAssessments, debugFillCurrentAssessment, getCurrentAssessment, answers } = useAssessment();
  const [isSaving, setIsSaving] = useState(false);

  const handleBackToDashboard = () => {
    router.push('/dashboard');
  };

  const handleDebugFillCurrent = () => {
    const currentAssessment = getCurrentAssessment();
    debugFillCurrentAssessment();
    alert(`Debug: Semua ${currentAssessment.questions.length} soal ${currentAssessment.name} telah diisi otomatis!`);
  };

  const handleDebugFillAll = () => {
    const confirmed = confirm('Debug: Apakah Anda yakin ingin mengisi SEMUA assessment (Big Five, RIASEC, VIA) dengan jawaban acak?');
    if (confirmed) {
      debugFillAllAssessments();
      alert('Debug: Semua assessment telah diisi otomatis dengan jawaban acak!');
    }
  };

  const handleSaveAndExit = async () => {
    setIsSaving(true);

    try {
      // Validate if all questions are answered
      const validation = validateAnswers(answers);

      if (validation.isValid) {
        // All questions answered - submit assessment and go to results
        const { resultId } = await submitAssessment(answers);

        // Add assessment to dashboard history with "Selesai" status
        const assessmentHistoryItem = {
          id: Date.now(),
          nama: "Assessment Lengkap",
          tipe: "Personality Assessment",
          tanggal: new Date().toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          }),
          status: "Selesai",
          resultId: resultId
        };

        // Save to localStorage for dashboard history
        const existingHistory = JSON.parse(localStorage.getItem('assessment-history') || '[]');
        existingHistory.unshift(assessmentHistoryItem);
        localStorage.setItem('assessment-history', JSON.stringify(existingHistory));

        // Navigate to results page
        router.push(`/results/${resultId}`);
      } else {
        // Not all questions answered - save progress and go to dashboard
        const assessmentHistoryItem = {
          id: Date.now(),
          nama: "Assessment Belum Selesai",
          tipe: "Personality Assessment",
          tanggal: new Date().toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          }),
          status: "Belum Selesai",
          resultId: null
        };

        // Save to localStorage for dashboard history
        const existingHistory = JSON.parse(localStorage.getItem('assessment-history') || '[]');
        existingHistory.unshift(assessmentHistoryItem);
        localStorage.setItem('assessment-history', JSON.stringify(existingHistory));

        // Also save current progress
        localStorage.setItem('assessment-progress', JSON.stringify(answers));

        // Navigate to dashboard
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Error saving assessment:', error);
      alert('Terjadi kesalahan saat menyimpan assessment. Silakan coba lagi.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="flex items-center justify-between px-8 py-6 bg-transparent">
      <div className="flex items-center gap-2">
        <button
          onClick={handleBackToDashboard}
          className="flex items-center gap-2 px-4 py-2 rounded-full border border-[#E5E7EB] bg-white text-[#64707D] text-[16px] font-medium shadow-sm hover:bg-[#f5f7fb] transition"
          type="button"
        >
          <img src="/icons/CaretLeft.svg" alt="Back" className="w-4 h-4" />
          Kembali ke Dashboard
        </button>
        <span className="font-semibold text-lg ml-4">{phase}: {assessmentName}</span>
      </div>
      <div className="flex items-center gap-6">
        {/* Debug Buttons - Only show in development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="flex gap-2">
            <button
              onClick={handleDebugFillCurrent}
              className="px-3 py-1 rounded-md bg-blue-500 hover:bg-blue-600 text-white text-xs font-semibold transition"
              title="Debug: Isi assessment saat ini"
            >
              🐛 Fill Current
            </button>
            <button
              onClick={handleDebugFillAll}
              className="px-3 py-1 rounded-md bg-orange-500 hover:bg-orange-600 text-white text-xs font-semibold transition"
              title="Debug: Isi semua assessment"
            >
              🐛 Fill All
            </button>
          </div>
        )}

        <button
          onClick={handleSaveAndExit}
          disabled={isSaving}
          className="text-[#6475e9] text-sm font-semibold hover:text-[#5a6bd8] disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving ? 'Menyimpan...' : 'Simpan & Keluar'}
        </button>
      </div>
    </div>
  );
}
