'use client';

import AssessmentSidebar from "./AssessmentSidebar";
import AssessmentHeader from "./AssessmentHeader";
import AssessmentProgressBar from "./AssessmentProgressBar";
import AssessmentQuestionsList from "./AssessmentQuestionsList";
import { AssessmentProvider, useAssessment } from '../../contexts/AssessmentContext';

function AssessmentContent() {
  const { getCurrentAssessment, currentSectionIndex } = useAssessment();
  const currentAssessment = getCurrentAssessment();

  const getPhaseNumber = () => {
    switch (currentAssessment.id) {
      case 'big-five': return '1';
      case 'riasec': return '2';
      case 'via-character': return '3';
      default: return '1';
    }
  };

  return (
    <div className="flex flex-row-reverse h-screen bg-[#f5f7fb]">
      {/* Fixed Sidebar */}
      <div className="fixed right-0 top-0 h-screen z-10">
        <AssessmentSidebar />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col mr-[280px]">
        <AssessmentHeader
          currentQuestion={currentSectionIndex + 1}
          totalQuestions={currentAssessment.totalQuestions}
          assessmentName={currentAssessment.name}
          phase={`Phase ${getPhaseNumber()}`}
        />
        <AssessmentProgressBar />
        <div className="flex-1 flex flex-col items-center justify-start overflow-y-auto py-8">
          {/* Render all questions vertically */}
          <AssessmentQuestionsList />
        </div>
      </div>
    </div>
  );
}

export default function AssessmentLayout() {
  return (
    <AssessmentProvider>
      <AssessmentContent />
    </AssessmentProvider>
  );
}
