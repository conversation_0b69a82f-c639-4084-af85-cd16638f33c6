"use client"

// Update the imports to use the new components and data
import { <PERSON><PERSON> } from "./components/dashboard/header"
import { StatsCard } from "./components/dashboard/stats-card"
import { AssessmentTable } from "./components/dashboard/assessment-table"
import { WorldMapCard } from "./components/dashboard/world-map-card"

import { ProgressCard } from "./components/dashboard/progress-card"
import { chartData } from "./data/mockData"
import { useAuth } from "./contexts/AuthContext"
import { calculateUserStats, formatStatsForDashboard, formatAssessmentHistory, calculateUserProgress } from "./services/user-stats"
import { useState, useEffect } from "react"
import type { StatCard, ProgressItem } from "./types/dashboard"

// Replace the existing component content with:
export default function Dashboard() {
  const { user } = useAuth();
  const [statsData, setStatsData] = useState<StatCard[]>([]);
  const [assessmentData, setAssessmentData] = useState<any[]>([]);
  const [progressData, setProgressData] = useState<ProgressItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load user data function
  const loadUserData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Calculate user statistics
      const userStats = await calculateUserStats(user.id);

      // Format data for dashboard components
      const formattedStats = formatStatsForDashboard(userStats);
      const formattedAssessments = formatAssessmentHistory(userStats);
      const formattedProgress = calculateUserProgress(userStats);

      // Update state
      setStatsData(formattedStats);
      setAssessmentData(formattedAssessments);
      setProgressData(formattedProgress);
    } catch (error) {
      console.error('Error loading user data:', error);
      // Keep default empty arrays on error
    } finally {
      setIsLoading(false);
    }
  };

  // Load user data on component mount and when user changes
  useEffect(() => {
    loadUserData();
  }, [user]);

  // Refresh data when window gains focus (user returns from assessment)
  useEffect(() => {
    const handleFocus = () => {
      loadUserData();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [user]);

  const handleExternalLink = () => {
    window.open("https://example.com", "_blank")
  }

  // Get user's display name
  const getUserDisplayName = () => {
    if (user?.name) {
      return user.name;
    }
    if (user?.email) {
      // Extract name from email (before @)
      return user.email.split('@')[0].charAt(0).toUpperCase() + user.email.split('@')[0].slice(1);
    }
    return 'User';
  };

  // Show loading state while data is being fetched
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#f5f7fb] p-6 flex items-center justify-center">
        <div className="max-w-[88rem] mx-auto space-y-6 w-full" style={{ transform: 'scale(1.2)', transformOrigin: 'center' }}>
          {/* Header */}
          <Header
            title={`Welcome, ${getUserDisplayName()}!`}
            description="Loading your personalized dashboard..."
            onExternalLinkClick={handleExternalLink}
          />

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Stats Cards Loading */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="bg-white rounded-lg p-4 animate-pulse">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>

              {/* Assessment Table Loading */}
              <div className="bg-white rounded-lg p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="h-4 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Sidebar Loading */}
            <div className="space-y-6">
              <div className="bg-white rounded-lg p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
              <div className="bg-white rounded-lg p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f5f7fb] p-6 flex items-center justify-center">
      <div className="max-w-[88rem] mx-auto space-y-6 w-full" style={{ transform: 'scale(1.2)', transformOrigin: 'center' }}>
        {/* Header */}
        <Header
          title={`Welcome, ${getUserDisplayName()}!`}
          description="Track your progress here, You almost reach your goal."
          onExternalLinkClick={handleExternalLink}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {statsData.map((stat) => (
                <StatsCard key={stat.id} stat={stat} />
              ))}
            </div>

            {/* Assessment History */}
            <AssessmentTable data={assessmentData} />
          </div>

          {/* Right Sidebar */}
          <div className="space-y-6">
            <WorldMapCard
              title={getUserDisplayName()}
              description="Discover your potential and prepare yourself to reach higher goals."
            />

            <ProgressCard
              title="RIASEC"
              description="Know where you can grow and contribute the most."
              data={progressData}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

