# 📊 Assessment Calculation Guide
## Panduan Perhitungan Assessment ATMA

Dokumen ini menjelaskan bagaimana sistem ATMA (AI-Driven Talent Mapping Assessment) menghitung dan memproses hasil assessment untuk menghasilkan profil talenta yang komprehensif.

---

## 🎯 Overview Sistem Assessment

ATMA menggunakan **3 instrumen psikometri** yang telah tervalidasi secara ilmiah:

| Assessment | Jumlah Pertanyaan | Dimensi | Skala |
|------------|-------------------|---------|-------|
| **VIA Character Strengths** | 96 | 24 kekuatan karakter | 1-5 |
| **RIASEC Holland Codes** | 60 | 6 tipe kepribadian karir | 1-5 |
| **Big Five (OCEAN)** | 50 | 5 trait kepribadian | 1-5 |

---

## 🔢 Proses Perhitungan Skor

### 1. Pengumpulan Jawaban Raw

Setiap pertanyaan dijawab dengan skala Likert 1-5:
- **1** = Very much unlike me / Sangat tidak seperti saya
- **2** = Unlike me / Tidak seperti saya  
- **3** = Neutral / Netral
- **4** = Like me / Seperti saya
- **5** = Very much like me / Sangat seperti saya

### 2. Algoritma Perhitungan Skor

```javascript
// Pseudocode untuk perhitungan skor per kategori
function calculateCategoryScore(answers, category) {
    let totalScore = 0;
    let questionCount = 0;
    
    // Proses pertanyaan regular
    for (question in category.questions) {
        if (answers[question] exists) {
            totalScore += answers[question];  // 1-5
            questionCount++;
        }
    }
    
    // Proses pertanyaan reverse (khusus Big Five)
    for (reverseQuestion in category.reverseQuestions) {
        if (answers[reverseQuestion] exists) {
            totalScore += (6 - answers[reverseQuestion]); // Reverse scoring
            questionCount++;
        }
    }
    
    // Hitung rata-rata dan konversi ke skala 0-100
    if (questionCount > 0) {
        averageScore = totalScore / questionCount;        // 1.0 - 5.0
        finalScore = Math.round(averageScore * 20);       // 0 - 100
        return finalScore;
    }
    
    return 0;
}
```

### 3. Reverse Scoring (Big Five)

Beberapa pertanyaan Big Five menggunakan **reverse scoring** untuk menghindari response bias:

```javascript
// Contoh pertanyaan reverse
Regular: "Is original, comes up with new ideas" → Score langsung
Reverse: "Prefers work that is routine" → Score = 6 - jawaban_asli

// Jika user menjawab 4 pada pertanyaan reverse:
// Score yang dihitung = 6 - 4 = 2
```

---

## 📋 Detail Perhitungan per Assessment

### VIA Character Strengths (24 Dimensi)

**Kategori Kekuatan Karakter:**
- **Wisdom & Knowledge**: Creativity, Curiosity, Judgment, Love of Learning, Perspective
- **Courage**: Bravery, Perseverance, Honesty, Zest
- **Humanity**: Love, Kindness, Social Intelligence
- **Justice**: Teamwork, Fairness, Leadership
- **Temperance**: Forgiveness, Humility, Prudence, Self-Regulation
- **Transcendence**: Appreciation of Beauty, Gratitude, Hope, Humor, Spirituality

**Perhitungan:**
- 4 pertanyaan per dimensi
- Rata-rata jawaban × 20 = skor 0-100
- Tidak ada reverse scoring

### RIASEC Holland Codes (6 Dimensi)

**Tipe Kepribadian Karir:**
- **Realistic (R)**: Praktis, hands-on, teknis
- **Investigative (I)**: Analitis, penelitian, sains
- **Artistic (A)**: Kreatif, ekspresif, inovatif
- **Social (S)**: Membantu orang, interpersonal
- **Enterprising (E)**: Kepemimpinan, persuasif, bisnis
- **Conventional (C)**: Terorganisir, detail, administratif

**Perhitungan:**
- 10 pertanyaan per dimensi
- Rata-rata jawaban × 20 = skor 0-100
- Tidak ada reverse scoring

### Big Five OCEAN (5 Dimensi)

**Trait Kepribadian:**
- **Openness**: Keterbukaan terhadap pengalaman baru
- **Conscientiousness**: Kehati-hatian dan kedisiplinan
- **Extraversion**: Orientasi sosial dan energi
- **Agreeableness**: Keramahan dan kerjasama
- **Neuroticism**: Stabilitas emosional (skor tinggi = kurang stabil)

**Perhitungan:**
- 8-12 pertanyaan per dimensi (regular + reverse)
- Reverse scoring diterapkan pada pertanyaan tertentu
- Rata-rata jawaban × 20 = skor 0-100

---

## 🔄 Transformasi Data untuk AI Analysis

### Format Data yang Dikirim ke Backend

```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    // ... 21 dimensi lainnya
  }
}
```

### Validasi Data

**Persyaratan Sebelum Submit:**
- ✅ Semua pertanyaan telah dijawab
- ✅ Skor dalam rentang 0-100 (integer)
- ✅ Token balance ≥ 1
- ✅ Format JSON sesuai spesifikasi API

---

## 🤖 Proses AI Analysis

### Tahapan Analisis

1. **Data Transformation** (Frontend)
   - Konversi skala 1-5 ke 0-100
   - Reverse scoring untuk Big Five
   - Validasi kelengkapan data

2. **AI Processing** (Backend)
   - Analisis pola kepribadian
   - Matching dengan database karir
   - Generasi rekomendasi personal

3. **Result Generation**
   - Persona profile creation
   - Career recommendations
   - Role model suggestions

### Status Monitoring

```javascript
// Real-time status tracking
Status: "queued" → "processing" → "completed"

// WebSocket notifications untuk update real-time
onAnalysisComplete: (data) => {
    navigate(`/results/${data.resultId}`);
}
```

---

## 📈 Interpretasi Hasil

### Skala Skor (0-100)

| Range | Interpretasi | Warna Indikator |
|-------|--------------|-----------------|
| 81-100 | **Very High** | 🟢 Hijau |
| 61-80 | **High** | 🔵 Biru |
| 41-60 | **Moderate** | 🟡 Kuning |
| 21-40 | **Low** | 🟠 Orange |
| 0-20 | **Very Low** | 🔴 Merah |

### Contoh Interpretasi

**RIASEC Profile: I(85) - R(75) - E(70)**
- **Dominan Investigative**: Cocok untuk karir research, analisis
- **Tinggi Realistic**: Suka hands-on, praktis
- **Moderate Enterprising**: Potensi leadership

**Big Five Profile: O(80) - C(65) - E(55) - A(45) - N(30)**
- **High Openness**: Kreatif, terbuka ide baru
- **Moderate Conscientiousness**: Cukup terorganisir
- **Low Neuroticism**: Stabil emosional

---

## 🎯 Output Akhir

### Struktur Hasil Assessment

```json
{
  "assessment_data": {
    "riasec": { /* skor 0-100 */ },
    "ocean": { /* skor 0-100 */ },
    "viaIs": { /* skor 0-100 */ }
  },
  "persona_profile": {
    "title": "The Innovative Analyst",
    "description": "Deskripsi kepribadian lengkap...",
    "strengths": ["Analytical thinking", "Creativity", "..."],
    "recommendations": ["Consider R&D roles", "..."],
    "careerRecommendation": [
      {
        "careerName": "Data Scientist",
        "careerProspect": {
          "jobAvailability": "high",
          "salaryPotential": "super high",
          "careerProgression": "high",
          "industryGrowth": "super high",
          "skillDevelopment": "high"
        }
      }
    ],
    "roleModel": ["Steve Jobs", "Marie Curie"]
  }
}
```

### Career Prospect Levels

- **Super High**: Sangat tinggi (>90%)
- **High**: Tinggi (70-90%)
- **Moderate**: Sedang (40-70%)
- **Low**: Rendah (20-40%)
- **Super Low**: Sangat rendah (<20%)

---

## 🔧 Technical Implementation

### Frontend Calculation

```javascript
// File: src/components/Assessment/AssessmentForm.jsx
const calculateScores = () => {
  const scores = {};
  
  Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
    let totalScore = 0;
    let questionCount = 0;
    
    // Regular questions
    category.questions.forEach((_, index) => {
      const questionKey = `${categoryKey}_${index}`;
      if (answers[questionKey]) {
        totalScore += answers[questionKey];
        questionCount++;
      }
    });
    
    // Reverse questions (Big Five only)
    if (category.reverseQuestions) {
      category.reverseQuestions.forEach((_, index) => {
        const questionKey = `${categoryKey}_reverse_${index}`;
        if (answers[questionKey]) {
          totalScore += (6 - answers[questionKey]); // Reverse scoring
          questionCount++;
        }
      });
    }
    
    // Calculate final score (0-100 scale)
    if (questionCount > 0) {
      scores[categoryKey] = Math.round((totalScore / questionCount) * 20);
    }
  });
  
  return scores;
};
```

### API Integration

```javascript
// File: src/services/apiService.js
async submitAssessment(assessmentData) {
  const response = await axios.post('/api/assessment/submit', {
    riasec: assessmentData.riasec,
    ocean: assessmentData.ocean,
    viaIs: assessmentData.viaIs
  });
  return response.data;
}
```

---

## 📚 Referensi Ilmiah

1. **VIA Character Strengths Survey**: Peterson & Seligman (2004)
2. **RIASEC Holland Codes**: Holland (1997) 
3. **Big Five Personality**: Costa & McCrae (1992)
4. **Psychometric Validation**: Cronbach's α > 0.80 untuk semua dimensi

---

*Dokumen ini dibuat untuk membantu developer dan stakeholder memahami mekanisme perhitungan assessment dalam sistem ATMA.*
